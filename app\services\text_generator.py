"""
Text Generation Service using Transformer models
"""

import asyncio
import time
from typing import Dict, Any, Optional
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    GenerationConfig,
    pipeline
)
import structlog

from app.models.schemas import TextGenerationRequest, GenerationStyle


class TextGenerator:
    """Text generation service using transformer models"""
    
    def __init__(self, model_name: str, device: str, cache_dir: str):
        self.model_name = model_name
        self.device = device
        self.cache_dir = cache_dir
        self.logger = structlog.get_logger(__name__)
        
        self.tokenizer: Optional[AutoTokenizer] = None
        self.model: Optional[AutoModelForCausalLM] = None
        self.generator = None
        self.is_loaded = False
        
    async def load_model(self) -> None:
        """Load the text generation model"""
        self.logger.info(f"Loading text model: {self.model_name}")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir,
                trust_remote_code=True
            )
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            # Create generation pipeline
            self.generator = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32
            )
            
            self.is_loaded = True
            self.logger.info("Text model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load text model: {e}")
            raise
    
    async def generate_text(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """Generate text based on the request"""
        if not self.is_loaded:
            raise RuntimeError("Text model not loaded")
        
        start_time = time.time()
        self.logger.info(f"Generating text for prompt: {request.prompt[:50]}...")
        
        try:
            # Prepare generation parameters
            generation_params = self._prepare_generation_params(request)
            
            # Generate text
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                self._generate_sync, 
                request.prompt, 
                generation_params
            )
            
            processing_time = time.time() - start_time
            
            # Extract generated text
            generated_text = result[0]["generated_text"]
            
            # Remove the original prompt from the generated text
            if generated_text.startswith(request.prompt):
                generated_text = generated_text[len(request.prompt):].strip()
            
            self.logger.info(f"Text generation completed in {processing_time:.2f}s")
            
            return {
                "generated_text": generated_text,
                "prompt": request.prompt,
                "processing_time": processing_time,
                "parameters": generation_params,
                "model_name": self.model_name
            }
            
        except Exception as e:
            self.logger.error(f"Text generation failed: {e}")
            raise
    
    def _generate_sync(self, prompt: str, params: Dict[str, Any]) -> Any:
        """Synchronous text generation"""
        return self.generator(
            prompt,
            **params,
            return_full_text=True,
            clean_up_tokenization_spaces=True
        )
    
    def _prepare_generation_params(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """Prepare generation parameters based on request and style"""
        
        # Base parameters
        params = {
            "max_new_tokens": min(request.max_length, 512),
            "temperature": request.temperature,
            "top_p": request.top_p,
            "do_sample": True,
            "pad_token_id": self.tokenizer.eos_token_id,
            "eos_token_id": self.tokenizer.eos_token_id,
        }
        
        # Adjust parameters based on style
        if request.style == GenerationStyle.CREATIVE:
            params.update({
                "temperature": min(request.temperature * 1.2, 1.5),
                "top_p": 0.95,
                "top_k": 50
            })
        elif request.style == GenerationStyle.PRECISE:
            params.update({
                "temperature": max(request.temperature * 0.8, 0.3),
                "top_p": 0.85,
                "top_k": 40
            })
        else:  # BALANCED
            params.update({
                "top_k": 45
            })
        
        # Set seed for reproducibility
        if request.seed is not None:
            torch.manual_seed(request.seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(request.seed)
        
        return params
    
    async def cleanup(self) -> None:
        """Cleanup model resources"""
        self.logger.info("Cleaning up text generator")
        
        if self.model is not None:
            del self.model
            self.model = None
            
        if self.tokenizer is not None:
            del self.tokenizer
            self.tokenizer = None
            
        if self.generator is not None:
            del self.generator
            self.generator = None
            
        self.is_loaded = False
        
        # Clear CUDA cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.logger.info("Text generator cleanup completed")
