"""
Model Manager for loading and managing AI models
"""

import asyncio
import time
from typing import Dict, Optional, Any
import torch
from transformers import <PERSON>Token<PERSON>, AutoModelForCausalLM
from diffusers import StableDiffusionPipeline
import structlog

from app.core.config import settings
from app.services.text_generator import TextGenerator
from app.services.image_generator import ImageGenerator
from app.services.multimodal_fusion import MultimodalFusion


class ModelManager:
    """Manages loading and lifecycle of AI models"""
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        self.text_generator: Optional[TextGenerator] = None
        self.image_generator: Optional[ImageGenerator] = None
        self.multimodal_fusion: Optional[MultimodalFusion] = None
        self.models_loaded: Dict[str, bool] = {
            "text": False,
            "image": False,
            "multimodal": False
        }
        self.start_time = time.time()
        
    async def initialize(self) -> None:
        """Initialize all models"""
        self.logger.info("Initializing model manager")
        
        # Check GPU availability
        device = "cuda" if torch.cuda.is_available() else "cpu"
        self.logger.info(f"Using device: {device}")
        
        try:
            # Initialize text generator
            await self._load_text_model(device)
            
            # Initialize image generator
            await self._load_image_model(device)
            
            # Initialize multimodal fusion
            await self._load_multimodal_fusion()
            
            self.logger.info("All models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize models: {e}")
            raise
    
    async def _load_text_model(self, device: str) -> None:
        """Load text generation model"""
        self.logger.info("Loading text generation model")
        
        try:
            self.text_generator = TextGenerator(
                model_name=settings.TEXT_MODEL_NAME,
                device=device,
                cache_dir=settings.MODEL_CACHE_DIR
            )
            await self.text_generator.load_model()
            self.models_loaded["text"] = True
            self.logger.info("Text model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load text model: {e}")
            raise
    
    async def _load_image_model(self, device: str) -> None:
        """Load image generation model"""
        self.logger.info("Loading image generation model")
        
        try:
            self.image_generator = ImageGenerator(
                model_name=settings.IMAGE_MODEL_NAME,
                device=device,
                cache_dir=settings.MODEL_CACHE_DIR
            )
            await self.image_generator.load_model()
            self.models_loaded["image"] = True
            self.logger.info("Image model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load image model: {e}")
            raise
    
    async def _load_multimodal_fusion(self) -> None:
        """Load multimodal fusion component"""
        self.logger.info("Initializing multimodal fusion")
        
        try:
            self.multimodal_fusion = MultimodalFusion(
                text_generator=self.text_generator,
                image_generator=self.image_generator
            )
            self.models_loaded["multimodal"] = True
            self.logger.info("Multimodal fusion initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize multimodal fusion: {e}")
            raise
    
    async def cleanup(self) -> None:
        """Cleanup models and free resources"""
        self.logger.info("Cleaning up model manager")
        
        if self.text_generator:
            await self.text_generator.cleanup()
            
        if self.image_generator:
            await self.image_generator.cleanup()
            
        # Clear CUDA cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        self.logger.info("Model manager cleanup completed")
    
    def get_status(self) -> Dict[str, Any]:
        """Get model manager status"""
        return {
            "models_loaded": self.models_loaded,
            "uptime": time.time() - self.start_time,
            "device": "cuda" if torch.cuda.is_available() else "cpu",
            "cuda_available": torch.cuda.is_available(),
            "memory_usage": self._get_memory_usage()
        }
    
    def _get_memory_usage(self) -> Dict[str, Any]:
        """Get memory usage information"""
        memory_info = {}
        
        if torch.cuda.is_available():
            memory_info["gpu_memory_allocated"] = torch.cuda.memory_allocated()
            memory_info["gpu_memory_reserved"] = torch.cuda.memory_reserved()
            memory_info["gpu_memory_total"] = torch.cuda.get_device_properties(0).total_memory
        
        return memory_info
