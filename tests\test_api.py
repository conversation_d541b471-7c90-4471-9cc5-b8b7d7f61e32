"""
API endpoint tests
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, AsyncMock
import json

from main import app
from app.services.model_manager import ModelManager


@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
def mock_model_manager():
    """Mock model manager fixture"""
    manager = Mock(spec=ModelManager)
    manager.models_loaded = {"text": True, "image": True, "multimodal": True}
    manager.get_status.return_value = {
        "models_loaded": {"text": True, "image": True, "multimodal": True},
        "uptime": 100.0,
        "device": "cpu",
        "cuda_available": False,
        "memory_usage": {}
    }
    
    # Mock generators
    manager.text_generator = Mock()
    manager.text_generator.generate_text = AsyncMock(return_value={
        "generated_text": "This is generated text.",
        "prompt": "Test prompt",
        "processing_time": 1.0,
        "parameters": {"temperature": 0.7},
        "model_name": "test-model"
    })
    
    manager.image_generator = Mock()
    manager.image_generator.generate_image = AsyncMock(return_value={
        "image_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        "prompt": "Test image prompt",
        "processing_time": 2.0,
        "parameters": {"width": 512, "height": 512},
        "model_name": "test-image-model",
        "format": "png"
    })
    
    manager.multimodal_fusion = Mock()
    manager.multimodal_fusion.generate_multimodal = AsyncMock(return_value={
        "text_result": {
            "generated_text": "Multimodal text",
            "prompt": "Test prompt",
            "processing_time": 1.0,
            "parameters": {},
            "model_name": "test-model"
        },
        "image_result": {
            "image_data": "data:image/png;base64,test",
            "prompt": "Test prompt",
            "processing_time": 2.0,
            "parameters": {},
            "model_name": "test-image-model",
            "format": "png"
        },
        "processing_time": 3.0,
        "fusion_metadata": {"coherence_score": 0.8},
        "enhanced_prompts": {"text": "enhanced", "image": "enhanced"}
    })
    
    return manager


def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["version"] == "1.0.0"


def test_text_generation(client, mock_model_manager, monkeypatch):
    """Test text generation endpoint"""
    # Mock the model manager in app state
    def mock_get_model_manager():
        return mock_model_manager
    
    monkeypatch.setattr("app.api.endpoints.generation.get_model_manager", lambda request: mock_model_manager)
    
    # Override the dependency
    app.dependency_overrides[lambda request: request.app.state.model_manager] = lambda: mock_model_manager
    
    request_data = {
        "prompt": "Generate a story about AI",
        "max_length": 100,
        "temperature": 0.7
    }
    
    response = client.post("/api/v1/generate/text", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "success"
    assert "generated_text" in data
    assert "request_id" in data
    assert data["prompt"] == request_data["prompt"]


def test_image_generation(client, mock_model_manager, monkeypatch):
    """Test image generation endpoint"""
    app.dependency_overrides[lambda request: request.app.state.model_manager] = lambda: mock_model_manager
    
    request_data = {
        "prompt": "A beautiful landscape",
        "width": 512,
        "height": 512,
        "num_inference_steps": 20
    }
    
    response = client.post("/api/v1/generate/image", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "success"
    assert "image_data" in data
    assert "request_id" in data
    assert data["prompt"] == request_data["prompt"]


def test_multimodal_generation(client, mock_model_manager, monkeypatch):
    """Test multimodal generation endpoint"""
    app.dependency_overrides[lambda request: request.app.state.model_manager] = lambda: mock_model_manager
    
    request_data = {
        "text_prompt": "A story about a magical forest",
        "generate_text": True,
        "generate_image": True,
        "fusion_strength": 0.7
    }
    
    response = client.post("/api/v1/generate/multimodal", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "success"
    assert "text_result" in data
    assert "image_result" in data
    assert "fusion_metadata" in data
    assert "request_id" in data


def test_invalid_text_request(client, mock_model_manager):
    """Test invalid text generation request"""
    app.dependency_overrides[lambda request: request.app.state.model_manager] = lambda: mock_model_manager
    
    request_data = {
        "prompt": "",  # Empty prompt should fail validation
        "max_length": 100
    }
    
    response = client.post("/api/v1/generate/text", json=request_data)
    assert response.status_code == 422  # Validation error


def test_model_not_loaded(client):
    """Test behavior when models are not loaded"""
    # Create a mock manager with no models loaded
    mock_manager = Mock(spec=ModelManager)
    mock_manager.models_loaded = {"text": False, "image": False, "multimodal": False}
    mock_manager.text_generator = None
    
    app.dependency_overrides[lambda request: request.app.state.model_manager] = lambda: mock_manager
    
    request_data = {
        "prompt": "Test prompt",
        "max_length": 100
    }
    
    response = client.post("/api/v1/generate/text", json=request_data)
    assert response.status_code == 503  # Service unavailable


# Cleanup
def teardown_module():
    """Clean up after tests"""
    app.dependency_overrides.clear()
