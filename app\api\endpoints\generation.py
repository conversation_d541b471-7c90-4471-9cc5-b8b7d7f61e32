"""
Generation API endpoints
"""

import uuid
import time
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
import structlog

from app.models.schemas import (
    TextGenerationRequest,
    TextGenerationResponse,
    ImageGenerationRequest,
    ImageGenerationResponse,
    MultimodalGenerationRequest,
    MultimodalGenerationResponse,
    ErrorResponse
)
from app.services.model_manager import ModelManager
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


def get_model_manager(request: Request) -> ModelManager:
    """Dependency to get model manager from app state"""
    return request.app.state.model_manager


@router.post("/text", response_model=TextGenerationResponse)
async def generate_text(
    request: TextGenerationRequest,
    model_manager: ModelManager = Depends(get_model_manager)
) -> TextGenerationResponse:
    """Generate text based on a prompt"""
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    logger.info(f"Text generation request {request_id}", prompt=request.prompt[:50])
    
    try:
        if not model_manager.text_generator or not model_manager.models_loaded["text"]:
            raise HTTPException(
                status_code=503,
                detail="Text generation model not available"
            )
        
        # Generate text
        result = await model_manager.text_generator.generate_text(request)
        
        processing_time = time.time() - start_time
        
        response = TextGenerationResponse(
            request_id=request_id,
            status="success",
            processing_time=processing_time,
            generated_text=result["generated_text"],
            prompt=result["prompt"],
            parameters=result["parameters"],
            metadata={
                "model_name": result["model_name"],
                "device": "cuda" if model_manager.text_generator.device == "cuda" else "cpu"
            }
        )
        
        logger.info(f"Text generation completed {request_id}", processing_time=processing_time)
        return response
        
    except Exception as e:
        logger.error(f"Text generation failed {request_id}", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Text generation failed: {str(e)}"
        )


@router.post("/image", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    model_manager: ModelManager = Depends(get_model_manager)
) -> ImageGenerationResponse:
    """Generate image based on a prompt"""
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    logger.info(f"Image generation request {request_id}", prompt=request.prompt[:50])
    
    try:
        if not model_manager.image_generator or not model_manager.models_loaded["image"]:
            raise HTTPException(
                status_code=503,
                detail="Image generation model not available"
            )
        
        # Generate image
        result = await model_manager.image_generator.generate_image(request)
        
        processing_time = time.time() - start_time
        
        response = ImageGenerationResponse(
            request_id=request_id,
            status="success",
            processing_time=processing_time,
            image_url="",  # Would be set if using cloud storage
            image_data=result["image_data"],
            prompt=result["prompt"],
            parameters=result["parameters"],
            metadata={
                "model_name": result["model_name"],
                "format": result["format"],
                "device": "cuda" if model_manager.image_generator.device == "cuda" else "cpu"
            }
        )
        
        logger.info(f"Image generation completed {request_id}", processing_time=processing_time)
        return response
        
    except Exception as e:
        logger.error(f"Image generation failed {request_id}", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Image generation failed: {str(e)}"
        )


@router.post("/multimodal", response_model=MultimodalGenerationResponse)
async def generate_multimodal(
    request: MultimodalGenerationRequest,
    model_manager: ModelManager = Depends(get_model_manager)
) -> MultimodalGenerationResponse:
    """Generate multimodal content (text and/or image)"""
    
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    logger.info(f"Multimodal generation request {request_id}", prompt=request.text_prompt[:50])
    
    try:
        if not model_manager.multimodal_fusion or not model_manager.models_loaded["multimodal"]:
            raise HTTPException(
                status_code=503,
                detail="Multimodal generation not available"
            )
        
        # Validate that at least one generation type is requested
        if not request.generate_text and not request.generate_image:
            raise HTTPException(
                status_code=400,
                detail="At least one of generate_text or generate_image must be True"
            )
        
        # Generate multimodal content
        result = await model_manager.multimodal_fusion.generate_multimodal(request)
        
        processing_time = time.time() - start_time
        
        # Prepare text result
        text_result = None
        if result["text_result"]:
            text_result = TextGenerationResponse(
                request_id=f"{request_id}_text",
                status="success",
                processing_time=result["text_result"]["processing_time"],
                generated_text=result["text_result"]["generated_text"],
                prompt=result["text_result"]["prompt"],
                parameters=result["text_result"]["parameters"],
                metadata={"model_name": result["text_result"]["model_name"]}
            )
        
        # Prepare image result
        image_result = None
        if result["image_result"]:
            image_result = ImageGenerationResponse(
                request_id=f"{request_id}_image",
                status="success",
                processing_time=result["image_result"]["processing_time"],
                image_url="",
                image_data=result["image_result"]["image_data"],
                prompt=result["image_result"]["prompt"],
                parameters=result["image_result"]["parameters"],
                metadata={
                    "model_name": result["image_result"]["model_name"],
                    "format": result["image_result"]["format"]
                }
            )
        
        response = MultimodalGenerationResponse(
            request_id=request_id,
            status="success",
            processing_time=processing_time,
            text_result=text_result,
            image_result=image_result,
            fusion_metadata=result["fusion_metadata"],
            metadata={
                "enhanced_prompts": result["enhanced_prompts"],
                "fusion_applied": True
            }
        )
        
        logger.info(f"Multimodal generation completed {request_id}", processing_time=processing_time)
        return response
        
    except Exception as e:
        logger.error(f"Multimodal generation failed {request_id}", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Multimodal generation failed: {str(e)}"
        )
