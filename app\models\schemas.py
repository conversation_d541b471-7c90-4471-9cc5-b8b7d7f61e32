"""
Pydantic schemas for API request/response models
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class GenerationStyle(str, Enum):
    """Available generation styles"""
    CREATIVE = "creative"
    BALANCED = "balanced"
    PRECISE = "precise"


class ImageFormat(str, Enum):
    """Supported image formats"""
    PNG = "png"
    JPEG = "jpeg"
    WEBP = "webp"


class TextGenerationRequest(BaseModel):
    """Request model for text generation"""
    prompt: str = Field(..., min_length=1, max_length=1000, description="Text prompt for generation")
    max_length: Optional[int] = Field(512, ge=1, le=2048, description="Maximum length of generated text")
    temperature: Optional[float] = Field(0.7, ge=0.1, le=2.0, description="Sampling temperature")
    top_p: Optional[float] = Field(0.9, ge=0.1, le=1.0, description="Top-p sampling parameter")
    style: Optional[GenerationStyle] = Field(GenerationStyle.BALANCED, description="Generation style")
    seed: Optional[int] = Field(None, description="Random seed for reproducibility")


class ImageGenerationRequest(BaseModel):
    """Request model for image generation"""
    prompt: str = Field(..., min_length=1, max_length=1000, description="Text prompt for image generation")
    negative_prompt: Optional[str] = Field(None, max_length=500, description="Negative prompt to avoid")
    width: Optional[int] = Field(512, ge=256, le=1024, description="Image width")
    height: Optional[int] = Field(512, ge=256, le=1024, description="Image height")
    num_inference_steps: Optional[int] = Field(50, ge=10, le=100, description="Number of denoising steps")
    guidance_scale: Optional[float] = Field(7.5, ge=1.0, le=20.0, description="Guidance scale")
    style: Optional[GenerationStyle] = Field(GenerationStyle.BALANCED, description="Generation style")
    format: Optional[ImageFormat] = Field(ImageFormat.PNG, description="Output image format")
    seed: Optional[int] = Field(None, description="Random seed for reproducibility")
    
    @validator("width", "height")
    def validate_dimensions(cls, v):
        if v % 64 != 0:
            raise ValueError("Width and height must be multiples of 64")
        return v


class MultimodalGenerationRequest(BaseModel):
    """Request model for multimodal generation"""
    text_prompt: str = Field(..., min_length=1, max_length=1000, description="Text prompt")
    image_prompt: Optional[str] = Field(None, max_length=1000, description="Image generation prompt")
    generate_text: bool = Field(True, description="Whether to generate text")
    generate_image: bool = Field(True, description="Whether to generate image")
    text_params: Optional[TextGenerationRequest] = Field(None, description="Text generation parameters")
    image_params: Optional[ImageGenerationRequest] = Field(None, description="Image generation parameters")
    fusion_strength: Optional[float] = Field(0.5, ge=0.0, le=1.0, description="Multimodal fusion strength")


class GenerationResponse(BaseModel):
    """Base response model for generation"""
    request_id: str = Field(..., description="Unique request identifier")
    status: str = Field(..., description="Generation status")
    processing_time: float = Field(..., description="Processing time in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class TextGenerationResponse(GenerationResponse):
    """Response model for text generation"""
    generated_text: str = Field(..., description="Generated text content")
    prompt: str = Field(..., description="Original prompt")
    parameters: Dict[str, Any] = Field(..., description="Generation parameters used")


class ImageGenerationResponse(GenerationResponse):
    """Response model for image generation"""
    image_url: str = Field(..., description="URL to generated image")
    image_data: Optional[str] = Field(None, description="Base64 encoded image data")
    prompt: str = Field(..., description="Original prompt")
    parameters: Dict[str, Any] = Field(..., description="Generation parameters used")


class MultimodalGenerationResponse(GenerationResponse):
    """Response model for multimodal generation"""
    text_result: Optional[TextGenerationResponse] = Field(None, description="Text generation result")
    image_result: Optional[ImageGenerationResponse] = Field(None, description="Image generation result")
    fusion_metadata: Dict[str, Any] = Field(default_factory=dict, description="Fusion process metadata")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request identifier if available")


class HealthResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="API version")
    models_loaded: Dict[str, bool] = Field(..., description="Model loading status")
    uptime: float = Field(..., description="Service uptime in seconds")
