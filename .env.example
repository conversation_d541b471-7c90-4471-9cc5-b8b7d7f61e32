# Application Configuration
APP_NAME=Multimodal Generative AI
VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS
ALLOWED_HOSTS=*

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/multimodal_ai

# Redis
REDIS_URL=redis://localhost:6379

# Model Configuration
TEXT_MODEL_NAME=microsoft/DialoGPT-medium
IMAGE_MODEL_NAME=runwayml/stable-diffusion-v1-5
MODEL_CACHE_DIR=./models

# Generation Settings
MAX_TEXT_LENGTH=512
MAX_IMAGE_SIZE=1024
DEFAULT_IMAGE_SIZE=512

# API Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
S3_BUCKET=

# Monitoring (Optional)
SENTRY_DSN=
ENABLE_METRICS=true

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
