"""
Multimodal Fusion Service for combining text and image generation
"""

import asyncio
import time
from typing import Dict, Any, Optional, Tuple
import structlog

from app.models.schemas import (
    MultimodalGenerationRequest, 
    TextGenerationRequest, 
    ImageGenerationRequest
)
from app.services.text_generator import TextGenerator
from app.services.image_generator import ImageGenerator


class MultimodalFusion:
    """Service for multimodal content generation and fusion"""
    
    def __init__(self, text_generator: TextGenerator, image_generator: ImageGenerator):
        self.text_generator = text_generator
        self.image_generator = image_generator
        self.logger = structlog.get_logger(__name__)
        
    async def generate_multimodal(self, request: MultimodalGenerationRequest) -> Dict[str, Any]:
        """Generate multimodal content based on the request"""
        start_time = time.time()
        self.logger.info(f"Starting multimodal generation for prompt: {request.text_prompt[:50]}...")
        
        try:
            # Prepare generation tasks
            tasks = []
            text_result = None
            image_result = None
            
            # Create enhanced prompts using fusion
            enhanced_prompts = self._create_enhanced_prompts(request)
            
            # Generate text if requested
            if request.generate_text:
                text_request = self._prepare_text_request(request, enhanced_prompts["text"])
                tasks.append(self._generate_text_task(text_request))
            
            # Generate image if requested
            if request.generate_image:
                image_request = self._prepare_image_request(request, enhanced_prompts["image"])
                tasks.append(self._generate_image_task(image_request))
            
            # Execute generation tasks
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                result_index = 0
                if request.generate_text:
                    text_result = results[result_index] if not isinstance(results[result_index], Exception) else None
                    result_index += 1
                
                if request.generate_image:
                    image_result = results[result_index] if not isinstance(results[result_index], Exception) else None
            
            processing_time = time.time() - start_time
            
            # Apply post-processing fusion
            fusion_metadata = self._apply_post_fusion(
                text_result, 
                image_result, 
                request.fusion_strength
            )
            
            self.logger.info(f"Multimodal generation completed in {processing_time:.2f}s")
            
            return {
                "text_result": text_result,
                "image_result": image_result,
                "processing_time": processing_time,
                "fusion_metadata": fusion_metadata,
                "enhanced_prompts": enhanced_prompts
            }
            
        except Exception as e:
            self.logger.error(f"Multimodal generation failed: {e}")
            raise
    
    def _create_enhanced_prompts(self, request: MultimodalGenerationRequest) -> Dict[str, str]:
        """Create enhanced prompts for better multimodal coherence"""
        
        base_prompt = request.text_prompt
        image_prompt = request.image_prompt or base_prompt
        
        # Enhance text prompt with visual context
        enhanced_text_prompt = base_prompt
        if request.generate_image:
            enhanced_text_prompt = f"{base_prompt} [Visual context: {image_prompt}]"
        
        # Enhance image prompt with textual context
        enhanced_image_prompt = image_prompt
        if request.generate_text:
            # Extract key visual elements from text prompt
            visual_keywords = self._extract_visual_keywords(base_prompt)
            if visual_keywords:
                enhanced_image_prompt = f"{image_prompt}, {', '.join(visual_keywords)}"
        
        return {
            "text": enhanced_text_prompt,
            "image": enhanced_image_prompt,
            "original_text": base_prompt,
            "original_image": image_prompt
        }
    
    def _extract_visual_keywords(self, text: str) -> list:
        """Extract visual keywords from text for image enhancement"""
        # Simple keyword extraction - in production, use more sophisticated NLP
        visual_words = [
            "bright", "dark", "colorful", "vibrant", "beautiful", "stunning",
            "landscape", "portrait", "abstract", "realistic", "artistic",
            "detailed", "high quality", "professional", "cinematic"
        ]
        
        keywords = []
        text_lower = text.lower()
        
        for word in visual_words:
            if word in text_lower:
                keywords.append(word)
        
        # Add style hints based on content
        if any(word in text_lower for word in ["story", "narrative", "tale"]):
            keywords.append("storytelling style")
        
        if any(word in text_lower for word in ["technical", "scientific", "analysis"]):
            keywords.append("technical illustration")
        
        return keywords[:3]  # Limit to top 3 keywords
    
    def _prepare_text_request(self, request: MultimodalGenerationRequest, enhanced_prompt: str) -> TextGenerationRequest:
        """Prepare text generation request"""
        if request.text_params:
            text_request = request.text_params.copy()
            text_request.prompt = enhanced_prompt
        else:
            text_request = TextGenerationRequest(prompt=enhanced_prompt)
        
        return text_request
    
    def _prepare_image_request(self, request: MultimodalGenerationRequest, enhanced_prompt: str) -> ImageGenerationRequest:
        """Prepare image generation request"""
        if request.image_params:
            image_request = request.image_params.copy()
            image_request.prompt = enhanced_prompt
        else:
            image_request = ImageGenerationRequest(prompt=enhanced_prompt)
        
        return image_request
    
    async def _generate_text_task(self, request: TextGenerationRequest) -> Dict[str, Any]:
        """Text generation task wrapper"""
        return await self.text_generator.generate_text(request)
    
    async def _generate_image_task(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """Image generation task wrapper"""
        return await self.image_generator.generate_image(request)
    
    def _apply_post_fusion(self, text_result: Optional[Dict], image_result: Optional[Dict], fusion_strength: float) -> Dict[str, Any]:
        """Apply post-processing fusion techniques"""
        
        fusion_metadata = {
            "fusion_strength": fusion_strength,
            "coherence_score": 0.0,
            "applied_techniques": []
        }
        
        # Calculate coherence score based on available results
        if text_result and image_result:
            # Simple coherence scoring - in production, use more sophisticated methods
            coherence_score = self._calculate_coherence_score(text_result, image_result)
            fusion_metadata["coherence_score"] = coherence_score
            fusion_metadata["applied_techniques"].append("cross_modal_coherence")
        
        # Apply fusion strength adjustments
        if fusion_strength > 0.7:
            fusion_metadata["applied_techniques"].append("high_fusion_strength")
        elif fusion_strength < 0.3:
            fusion_metadata["applied_techniques"].append("low_fusion_strength")
        
        return fusion_metadata
    
    def _calculate_coherence_score(self, text_result: Dict, image_result: Dict) -> float:
        """Calculate coherence score between text and image"""
        # Simplified coherence calculation
        # In production, use CLIP or similar models for semantic similarity
        
        text_prompt = text_result.get("prompt", "")
        image_prompt = image_result.get("prompt", "")
        
        # Basic word overlap scoring
        text_words = set(text_prompt.lower().split())
        image_words = set(image_prompt.lower().split())
        
        if not text_words or not image_words:
            return 0.5
        
        overlap = len(text_words.intersection(image_words))
        total_unique = len(text_words.union(image_words))
        
        coherence_score = overlap / total_unique if total_unique > 0 else 0.0
        
        # Normalize to 0-1 range
        return min(max(coherence_score, 0.0), 1.0)
