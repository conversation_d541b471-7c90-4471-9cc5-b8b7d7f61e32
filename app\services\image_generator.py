"""
Image Generation Service using Diffusion models
"""

import asyncio
import time
import io
import base64
from typing import Dict, Any, Optional
import torch
from diffusers import StableDiffusionPipeline, DPMSolverMultistepScheduler
from PIL import Image
import structlog

from app.models.schemas import ImageGenerationRequest, GenerationStyle, ImageFormat


class ImageGenerator:
    """Image generation service using diffusion models"""
    
    def __init__(self, model_name: str, device: str, cache_dir: str):
        self.model_name = model_name
        self.device = device
        self.cache_dir = cache_dir
        self.logger = structlog.get_logger(__name__)
        
        self.pipeline: Optional[StableDiffusionPipeline] = None
        self.is_loaded = False
        
    async def load_model(self) -> None:
        """Load the image generation model"""
        self.logger.info(f"Loading image model: {self.model_name}")
        
        try:
            # Load the pipeline
            self.pipeline = StableDiffusionPipeline.from_pretrained(
                self.model_name,
                cache_dir=self.cache_dir,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                safety_checker=None,
                requires_safety_checker=False
            )
            
            # Use DPM solver for faster inference
            self.pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                self.pipeline.scheduler.config
            )
            
            # Move to device
            self.pipeline = self.pipeline.to(self.device)
            
            # Enable memory efficient attention if on CUDA
            if self.device == "cuda":
                self.pipeline.enable_attention_slicing()
                self.pipeline.enable_xformers_memory_efficient_attention()
            
            self.is_loaded = True
            self.logger.info("Image model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load image model: {e}")
            raise
    
    async def generate_image(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """Generate image based on the request"""
        if not self.is_loaded:
            raise RuntimeError("Image model not loaded")
        
        start_time = time.time()
        self.logger.info(f"Generating image for prompt: {request.prompt[:50]}...")
        
        try:
            # Prepare generation parameters
            generation_params = self._prepare_generation_params(request)
            
            # Generate image
            loop = asyncio.get_event_loop()
            image = await loop.run_in_executor(
                None, 
                self._generate_sync, 
                request.prompt,
                generation_params
            )
            
            processing_time = time.time() - start_time
            
            # Convert image to base64
            image_data = self._image_to_base64(image, request.format)
            
            self.logger.info(f"Image generation completed in {processing_time:.2f}s")
            
            return {
                "image": image,
                "image_data": image_data,
                "prompt": request.prompt,
                "processing_time": processing_time,
                "parameters": generation_params,
                "model_name": self.model_name,
                "format": request.format.value
            }
            
        except Exception as e:
            self.logger.error(f"Image generation failed: {e}")
            raise
    
    def _generate_sync(self, prompt: str, params: Dict[str, Any]) -> Image.Image:
        """Synchronous image generation"""
        result = self.pipeline(prompt, **params)
        return result.images[0]
    
    def _prepare_generation_params(self, request: ImageGenerationRequest) -> Dict[str, Any]:
        """Prepare generation parameters based on request and style"""
        
        # Base parameters
        params = {
            "height": request.height,
            "width": request.width,
            "num_inference_steps": request.num_inference_steps,
            "guidance_scale": request.guidance_scale,
            "negative_prompt": request.negative_prompt,
            "num_images_per_prompt": 1,
        }
        
        # Adjust parameters based on style
        if request.style == GenerationStyle.CREATIVE:
            params.update({
                "guidance_scale": min(request.guidance_scale * 1.2, 15.0),
                "num_inference_steps": max(request.num_inference_steps + 10, 60)
            })
        elif request.style == GenerationStyle.PRECISE:
            params.update({
                "guidance_scale": max(request.guidance_scale * 0.9, 5.0),
                "num_inference_steps": max(request.num_inference_steps + 20, 70)
            })
        
        # Set seed for reproducibility
        if request.seed is not None:
            generator = torch.Generator(device=self.device)
            generator.manual_seed(request.seed)
            params["generator"] = generator
        
        return params
    
    def _image_to_base64(self, image: Image.Image, format: ImageFormat) -> str:
        """Convert PIL Image to base64 string"""
        buffer = io.BytesIO()
        
        # Convert format enum to PIL format
        pil_format = format.value.upper()
        if pil_format == "JPEG":
            pil_format = "JPEG"
            # Convert RGBA to RGB for JPEG
            if image.mode == "RGBA":
                background = Image.new("RGB", image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])
                image = background
        
        image.save(buffer, format=pil_format, quality=95 if pil_format == "JPEG" else None)
        image_data = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/{format.value};base64,{image_data}"
    
    async def cleanup(self) -> None:
        """Cleanup model resources"""
        self.logger.info("Cleaning up image generator")
        
        if self.pipeline is not None:
            del self.pipeline
            self.pipeline = None
            
        self.is_loaded = False
        
        # Clear CUDA cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.logger.info("Image generator cleanup completed")
