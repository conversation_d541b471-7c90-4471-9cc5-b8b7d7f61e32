"""
Health check API endpoints
"""

import time
from fastapi import APIRouter, Depends, Request
from app.models.schemas import HealthResponse
from app.services.model_manager import ModelManager

router = APIRouter()


def get_model_manager(request: Request) -> ModelManager:
    """Dependency to get model manager from app state"""
    return request.app.state.model_manager


@router.get("/", response_model=HealthResponse)
async def health_check(
    model_manager: ModelManager = Depends(get_model_manager)
) -> HealthResponse:
    """Comprehensive health check endpoint"""
    
    status = model_manager.get_status()
    
    return HealthResponse(
        status="healthy" if all(status["models_loaded"].values()) else "degraded",
        version="1.0.0",
        models_loaded=status["models_loaded"],
        uptime=status["uptime"]
    )


@router.get("/models")
async def models_status(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """Detailed model status endpoint"""
    return model_manager.get_status()


@router.get("/ready")
async def readiness_check(
    model_manager: ModelManager = Depends(get_model_manager)
):
    """Kubernetes readiness probe endpoint"""
    status = model_manager.get_status()
    
    if all(status["models_loaded"].values()):
        return {"status": "ready"}
    else:
        return {"status": "not_ready", "models_loaded": status["models_loaded"]}


@router.get("/live")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    return {"status": "alive", "timestamp": time.time()}
