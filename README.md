# Multimodal Generative AI System

A comprehensive multimodal AI system that generates realistic text and images based on user input, with support for multimodal fusion and deployed as a REST API.

## Features

### Core Capabilities
- **Text Generation**: Generate realistic and coherent text using transformer models
- **Image Generation**: Create high-quality images using diffusion models
- **Multimodal Generation**: Generate semantically related text and images
- **Style Control**: Adjust generation style (Creative, Balanced, Precise)
- **REST API**: Easy integration with comprehensive API endpoints

### Advanced Features
- **Multimodal Fusion**: Semantic coherence between text and image outputs
- **Scalable Architecture**: Handle concurrent requests efficiently
- **GPU Acceleration**: CUDA support for improved performance
- **Authentication & Security**: JWT-based authentication with rate limiting
- **Monitoring & Logging**: Structured logging and health checks
- **Docker Support**: Containerized deployment with GPU support

## Architecture

### System Components
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Model Manager   │────│  Text Generator │
│   (FastAPI)     │    │                  │    │  (Transformers) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │              ┌─────────────────┐
         │                       └──────────────│ Image Generator │
         │                                      │   (Diffusion)   │
         │                                      └─────────────────┘
         │                                               │
         │              ┌──────────────────┐            │
         └──────────────│ Multimodal Fusion│────────────┘
                        │    Component     │
                        └──────────────────┘
```

### Data Flow
1. **User Request** → API Gateway
2. **Request Processing** → Model Manager
3. **Generation** → Appropriate service(s)
4. **Fusion** → Multimodal component (if applicable)
5. **Response** → User

## Installation

### Prerequisites
- Python 3.10+
- CUDA 11.8+ (for GPU support)
- Docker & Docker Compose (for containerized deployment)
- 8GB+ RAM (16GB+ recommended)
- GPU with 8GB+ VRAM (recommended)

### Local Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd multimodal-ai-system
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Run the application**
```bash
python main.py
```

### Docker Deployment

1. **Build and run with Docker Compose**
```bash
docker-compose up --build
```

2. **For GPU support**
```bash
# Ensure NVIDIA Docker runtime is installed
docker-compose up --build
```

## API Usage

### Base URL
```
http://localhost:8000/api/v1
```

### Authentication
Include JWT token in Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Endpoints

#### Text Generation
```bash
POST /generate/text
Content-Type: application/json

{
  "prompt": "Write a story about artificial intelligence",
  "max_length": 512,
  "temperature": 0.7,
  "style": "creative"
}
```

#### Image Generation
```bash
POST /generate/image
Content-Type: application/json

{
  "prompt": "A futuristic cityscape at sunset",
  "width": 512,
  "height": 512,
  "num_inference_steps": 50,
  "style": "balanced"
}
```

#### Multimodal Generation
```bash
POST /generate/multimodal
Content-Type: application/json

{
  "text_prompt": "A magical forest with glowing trees",
  "generate_text": true,
  "generate_image": true,
  "fusion_strength": 0.7
}
```

### Response Format
```json
{
  "request_id": "uuid-string",
  "status": "success",
  "processing_time": 2.34,
  "generated_text": "...",
  "image_data": "data:image/png;base64,...",
  "metadata": {...}
}
```

## Configuration

### Environment Variables
See `.env.example` for all available configuration options.

Key settings:
- `TEXT_MODEL_NAME`: Hugging Face model for text generation
- `IMAGE_MODEL_NAME`: Hugging Face model for image generation
- `MODEL_CACHE_DIR`: Directory for cached models
- `MAX_TEXT_LENGTH`: Maximum text generation length
- `RATE_LIMIT_PER_MINUTE`: API rate limiting

### Model Configuration
The system supports various models:

**Text Models:**
- `microsoft/DialoGPT-medium` (default)
- `gpt2-large`
- `facebook/blenderbot-400M-distill`

**Image Models:**
- `runwayml/stable-diffusion-v1-5` (default)
- `stabilityai/stable-diffusion-2-1`
- `CompVis/stable-diffusion-v1-4`

## Development

### Project Structure
```
multimodal-ai-system/
├── app/
│   ├── api/              # API routes and endpoints
│   ├── core/             # Core configuration and utilities
│   ├── models/           # Pydantic schemas
│   ├── services/         # AI generation services
│   └── auth/             # Authentication and security
├── tests/                # Test suite
├── docker/               # Docker configuration
├── docs/                 # Documentation
├── main.py              # Application entry point
├── requirements.txt     # Python dependencies
└── docker-compose.yml   # Container orchestration
```

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=app --cov-report=html
```

### Code Quality
```bash
# Format code
black app/ tests/

# Lint code
flake8 app/ tests/

# Type checking
mypy app/
```

## Monitoring

### Health Checks
- `/health` - Overall system health
- `/health/models` - Model loading status
- `/health/ready` - Kubernetes readiness probe
- `/health/live` - Kubernetes liveness probe

### Logging
Structured JSON logging with configurable levels:
```python
import structlog
logger = structlog.get_logger(__name__)
logger.info("Generation completed", processing_time=2.34)
```

### Metrics
Prometheus metrics available at `/metrics` (when enabled)

## Deployment

### Production Considerations
1. **Security**: Change default secret keys and enable HTTPS
2. **Scaling**: Use multiple replicas behind a load balancer
3. **Storage**: Configure cloud storage for generated content
4. **Monitoring**: Set up logging aggregation and alerting
5. **GPU Resources**: Ensure adequate GPU memory and compute

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: multimodal-ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: multimodal-ai
  template:
    spec:
      containers:
      - name: multimodal-ai
        image: multimodal-ai:latest
        resources:
          limits:
            nvidia.com/gpu: 1
            memory: 16Gi
          requests:
            memory: 8Gi
```

## Performance Optimization

### GPU Optimization
- Enable mixed precision training
- Use attention slicing for memory efficiency
- Implement model quantization for faster inference

### Caching
- Model caching to reduce load times
- Response caching for repeated requests
- Redis for session and rate limit storage

### Scaling
- Horizontal scaling with load balancing
- Async processing for long-running tasks
- Queue-based architecture for high throughput

## Troubleshooting

### Common Issues
1. **CUDA Out of Memory**: Reduce batch size or image resolution
2. **Model Loading Errors**: Check model cache directory permissions
3. **Slow Generation**: Verify GPU utilization and model optimization
4. **API Timeouts**: Increase timeout settings for large requests

### Debug Mode
Enable debug logging:
```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
python main.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the troubleshooting guide
